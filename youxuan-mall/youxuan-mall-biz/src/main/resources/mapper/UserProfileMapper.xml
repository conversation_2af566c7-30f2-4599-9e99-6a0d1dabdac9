<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ffnex.youxuan.mall.mapper.UserProfileMapper">

  <resultMap id="userProfileMap" type="com.ffnex.youxuan.mall.entity.UserProfileEntity">
        <id property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="partnerUserId" column="partner_user_id"/>
        <result property="customerLevel" column="customer_level"/>
        <result property="name" column="name"/>
        <result property="avatar" column="avatar"/>
        <result property="gender" column="gender"/>
        <result property="phone" column="phone"/>
        <result property="email" column="email"/>
        <result property="birthday" column="birthday"/>
        <result property="province" column="province"/>
        <result property="city" column="city"/>
        <result property="district" column="district"/>
        <result property="address" column="address"/>
        <result property="idCard" column="id_card"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="tenantId" column="tenant_id"/>
  </resultMap>
</mapper>
