<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ffnex.youxuan.mall.mapper.CustomerPricingRuleMapper">

  <resultMap id="customerPricingRuleMap" type="com.ffnex.youxuan.mall.entity.CustomerPricingRuleEntity">
        <id property="id" column="id"/>
        <result property="skuId" column="sku_id"/>
        <result property="userId" column="user_id"/>
        <result property="price" column="price"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="tenantId" column="tenant_id"/>
  </resultMap>
</mapper>
