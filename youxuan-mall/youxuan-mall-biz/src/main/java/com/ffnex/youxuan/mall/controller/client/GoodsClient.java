package com.ffnex.youxuan.mall.controller.client;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ffnex.youxuan.admin.api.entity.SysUser;
import com.ffnex.youxuan.admin.api.feign.RemoteUserService;
import com.ffnex.youxuan.common.core.util.R;
import com.ffnex.youxuan.common.core.util.RetOps;
import com.ffnex.youxuan.common.security.annotation.Inner;
import com.ffnex.youxuan.mall.dto.CartGoodsDTO;
import com.ffnex.youxuan.mall.dto.UserInfoDTO;
import com.ffnex.youxuan.mall.entity.*;
import com.ffnex.youxuan.mall.service.*;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@Inner(value = false)
@RequestMapping("/client/goods")
@RequiredArgsConstructor
public class GoodsClient {

    private final GoodsService goodsService;
    private final GoodsSkuService goodsSkuService;
    private final GoodsServiceTagService goodsServiceTagService;
    private final OrderCommentService orderCommentService;
    private final RemoteUserService remoteUserService;
    private final TieredPricingRuleService tieredPricingRuleService;

    @GetMapping("/recommended")
    public R<List<GoodsEntity>> recommendedList(@RequestParam("code") String positionCode) {
        return R.ok(goodsService.listRecommended(positionCode));
    }


    @GetMapping("list")
    public R<List<GoodsEntity>> list(@RequestParam(value = "category_id", required = false) Long categoryId,
                                     @RequestParam(value = "keyword", required = false) String keyword) {
        LambdaQueryWrapper<GoodsEntity> wp = Wrappers.lambdaQuery(GoodsEntity.class).eq(GoodsEntity::getStatus, 1);
        if (categoryId != null) {
            wp.eq(GoodsEntity::getCategoryId, categoryId);
        }
        if (StringUtils.hasText(keyword)) {
            wp.like(GoodsEntity::getName, keyword);
        }
        return R.ok(goodsService.list(wp));
    }

    @GetMapping("detail")
    public R<GoodsEntity> detail(@RequestParam("id") long id) {
        return R.ok(goodsService.getById(id));
    }

    @GetMapping("sku_list")
    public R<List<GoodsSkuEntity>> skuList(@RequestParam("goods_id") long goodsId) {
        return R.ok(goodsSkuService.list(Wrappers.lambdaQuery(GoodsSkuEntity.class).eq(GoodsSkuEntity::getGoodsId, goodsId)));
    }

    @GetMapping("tag_list")
    public R<List<GoodsServiceTagEntity>> tagList(@RequestParam("goods_id") long goodsId) {
        return R.ok(goodsServiceTagService.listDeep(Wrappers.lambdaQuery(GoodsServiceTagEntity.class).eq(GoodsServiceTagEntity::getGoodsId, goodsId)));
    }

    @GetMapping("sku_detail")
    public R<CartGoodsDTO> skuDetail(@RequestParam("skuId") long skuId) {
        GoodsSkuEntity sku = goodsSkuService.getByIdDeep(skuId);
        if (sku == null) {
            return R.failed();
        }
        CartGoodsDTO cartGoodsDTO = new CartGoodsDTO();
        cartGoodsDTO.setGoodsId(sku.getGoodsId());
        cartGoodsDTO.setGoodsName(sku.getGoods().getName());
        cartGoodsDTO.setGoodsImage(sku.getGoods().getImage());
        cartGoodsDTO.setSkuId(skuId);
        cartGoodsDTO.setSkuName(sku.getName());
        cartGoodsDTO.setActualPrice(sku.getActualPrice());
        cartGoodsDTO.setOriginalPrice(sku.getOriginalPrice());
        cartGoodsDTO.setStock(sku.getStock());
        cartGoodsDTO.setQuantity(1);

        return R.ok(cartGoodsDTO);
    }


    /**
     * 获取商品评价
     */
    @GetMapping("/comment")
    public R<List<OrderCommentEntity>> comment(@RequestParam("goodsId") Long goodsId) {

        List<OrderCommentEntity> list = orderCommentService.lambdaQuery().eq(OrderCommentEntity::getGoodsId, goodsId).orderByDesc(OrderCommentEntity::getCreateTime).list();
        List<Long> userIds = list.stream().map(OrderCommentEntity::getUserId).toList();
        if (CollUtil.isNotEmpty(userIds)) {
            List<SysUser> sysUsers = RetOps.of(remoteUserService.getUserById(userIds)).getData().orElse(List.of());
            list.forEach(item -> {
                item.setUser(sysUsers.stream().map(u -> {
                    UserInfoDTO userInfoDTO = new UserInfoDTO();
                    BeanUtils.copyProperties(u, userInfoDTO);
                    return userInfoDTO;
                }).filter(w -> w.getUserId().longValue() == item.getUserId().longValue()).findAny().orElse(null));
            });
        }

        return R.ok(list);
    }

    /**
     * 获取sku的阶梯价格
     */
    @GetMapping("/tiered_pricing")
    public R<List<TieredPricingRuleEntity>> tieredPricing(@RequestParam("skuId") Long skuId) {
        return R.ok(tieredPricingRuleService.list(Wrappers.lambdaQuery(TieredPricingRuleEntity.class).eq(TieredPricingRuleEntity::getSkuId, skuId)));
    }
}
