package com.ffnex.youxuan.mall.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.ffnex.youxuan.common.core.util.TenantTable;
import com.github.yulichang.annotation.FieldMapping;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 客户定价规则
 *
 * <AUTHOR>
 * @date 2025-08-19 21:54:31
 */
@Data
@TenantTable
@TableName("customer_pricing_rule")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "客户定价规则")
public class CustomerPricingRuleEntity extends Model<CustomerPricingRuleEntity> {


	/**
	* ID
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="ID")
    private Long id;

	/**
	* 商品ID
	*/
    @Schema(description="商品ID")
    private Long skuId;

	/**
	* 用户ID
	*/
    @Schema(description="用户ID")
    private Long userId;

	/**
	* 价格
	*/
    @Schema(description="价格")
    private BigDecimal price;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 更新时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新时间")
    private LocalDateTime updateTime;

	/**
	* 租户
	*/
    @Schema(description="租户")
    private Long tenantId;

    @TableField(exist = false)
    @FieldMapping(thisField = "userId", joinField = "userId", tag = UserProfileEntity.class, select = "phone")
    private String phone;
}