package com.ffnex.youxuan.mall.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ffnex.youxuan.common.core.util.R;
import com.ffnex.youxuan.common.log.annotation.SysLog;
import com.ffnex.youxuan.common.excel.annotation.ResponseExcel;
import com.ffnex.youxuan.common.excel.annotation.RequestExcel;
import com.ffnex.youxuan.mall.entity.PartnerMonthlyCommissionEntity;
import com.ffnex.youxuan.mall.service.PartnerMonthlyCommissionService;

import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import com.ffnex.youxuan.common.security.annotation.HasPermission;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 合伙人月度提成
 *
 * <AUTHOR>
 * @date 2025-08-19 21:50:55
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/partnerMonthlyCommission" )
@Tag(description = "partnerMonthlyCommission" , name = "合伙人月度提成管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class PartnerMonthlyCommissionController {

    private final  PartnerMonthlyCommissionService partnerMonthlyCommissionService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param partnerMonthlyCommission 合伙人月度提成
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @HasPermission("mall_partnerMonthlyCommission_view")
    public R getPartnerMonthlyCommissionPage(@ParameterObject Page page, @ParameterObject PartnerMonthlyCommissionEntity partnerMonthlyCommission) {
        LambdaQueryWrapper<PartnerMonthlyCommissionEntity> wrapper = Wrappers.lambdaQuery();
		wrapper.eq(Objects.nonNull(partnerMonthlyCommission.getStatus()),PartnerMonthlyCommissionEntity::getStatus,partnerMonthlyCommission.getStatus());
        return R.ok(partnerMonthlyCommissionService.page(page, wrapper));
    }


    /**
     * 通过条件查询合伙人月度提成
     * @param partnerMonthlyCommission 查询条件
     * @return R  对象列表
     */
    @Operation(summary = "通过条件查询" , description = "通过条件查询对象" )
    @GetMapping("/details" )
    @HasPermission("mall_partnerMonthlyCommission_view")
    public R getDetails(@ParameterObject PartnerMonthlyCommissionEntity partnerMonthlyCommission) {
        return R.ok(partnerMonthlyCommissionService.list(Wrappers.query(partnerMonthlyCommission)));
    }

    /**
     * 新增合伙人月度提成
     * @param partnerMonthlyCommission 合伙人月度提成
     * @return R
     */
    @Operation(summary = "新增合伙人月度提成" , description = "新增合伙人月度提成" )
    @SysLog("新增合伙人月度提成" )
    @PostMapping
    @HasPermission("mall_partnerMonthlyCommission_add")
    public R save(@RequestBody PartnerMonthlyCommissionEntity partnerMonthlyCommission) {
        return R.ok(partnerMonthlyCommissionService.save(partnerMonthlyCommission));
    }

    /**
     * 修改合伙人月度提成
     * @param partnerMonthlyCommission 合伙人月度提成
     * @return R
     */
    @Operation(summary = "修改合伙人月度提成" , description = "修改合伙人月度提成" )
    @SysLog("修改合伙人月度提成" )
    @PutMapping
    @HasPermission("mall_partnerMonthlyCommission_edit")
    public R updateById(@RequestBody PartnerMonthlyCommissionEntity partnerMonthlyCommission) {
        return R.ok(partnerMonthlyCommissionService.updateById(partnerMonthlyCommission));
    }

    /**
     * 通过id删除合伙人月度提成
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除合伙人月度提成" , description = "通过id删除合伙人月度提成" )
    @SysLog("通过id删除合伙人月度提成" )
    @DeleteMapping
    @HasPermission("mall_partnerMonthlyCommission_del")
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(partnerMonthlyCommissionService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param partnerMonthlyCommission 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @HasPermission("mall_partnerMonthlyCommission_export")
    public List<PartnerMonthlyCommissionEntity> exportExcel(PartnerMonthlyCommissionEntity partnerMonthlyCommission,Long[] ids) {
        return partnerMonthlyCommissionService.list(Wrappers.lambdaQuery(partnerMonthlyCommission).in(ArrayUtil.isNotEmpty(ids), PartnerMonthlyCommissionEntity::getId, ids));
    }

    /**
     * 导入excel 表
     * @param partnerMonthlyCommissionList 对象实体列表
     * @param bindingResult 错误信息列表
     * @return ok fail
     */
    @PostMapping("/import")
    @HasPermission("mall_partnerMonthlyCommission_export")
    public R importExcel(@RequestExcel List<PartnerMonthlyCommissionEntity> partnerMonthlyCommissionList, BindingResult bindingResult) {
        return R.ok(partnerMonthlyCommissionService.saveBatch(partnerMonthlyCommissionList));
    }
}
