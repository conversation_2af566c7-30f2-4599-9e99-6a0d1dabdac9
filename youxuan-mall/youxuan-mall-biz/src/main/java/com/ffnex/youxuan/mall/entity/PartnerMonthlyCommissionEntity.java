package com.ffnex.youxuan.mall.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.ffnex.youxuan.common.core.util.TenantTable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 合伙人月度提成
 *
 * <AUTHOR>
 * @date 2025-08-19 21:50:55
 */
@Data
@TenantTable
@TableName("partner_monthly_commission")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "合伙人月度提成")
public class PartnerMonthlyCommissionEntity extends Model<PartnerMonthlyCommissionEntity> {


	/**
	* ID
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="ID")
    private Long id;

	/**
	* 合伙人ID
	*/
    @Schema(description="合伙人ID")
    private Long partnerId;

	/**
	* 年月
	*/
    @Schema(description="年月")
    private String yearMonth;

	/**
	* 提成金额
	*/
    @Schema(description="提成金额")
    private BigDecimal amount;

	/**
	* 状态
	*/
    @Schema(description="状态")
    private Integer status;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 更新时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新时间")
    private LocalDateTime updateTime;

	/**
	* 租户
	*/
    @Schema(description="租户")
    private Long tenantId;
}