package com.ffnex.youxuan.mall.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.ffnex.youxuan.common.core.util.TenantTable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 阶梯定价规则
 *
 * <AUTHOR>
 * @date 2025-08-19 21:53:44
 */
@Data
@TenantTable
@TableName("tiered_pricing_rule")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "阶梯定价规则")
public class TieredPricingRuleEntity extends Model<TieredPricingRuleEntity> {


	/**
	* ID
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="ID")
    private Long id;

	/**
	* 商品ID
	*/
    @Schema(description="商品ID")
    private Long skuId;

	/**
	* 最小数量
	*/
    @Schema(description="最小数量")
    private Integer minQuantity;

	/**
	* 最大数量
	*/
    @Schema(description="最大数量")
    private Integer maxQuantity;

	/**
	* 价格
	*/
    @Schema(description="价格")
    private BigDecimal price;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 更新时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新时间")
    private LocalDateTime updateTime;

	/**
	* 租户
	*/
    @Schema(description="租户")
    private Long tenantId;
}