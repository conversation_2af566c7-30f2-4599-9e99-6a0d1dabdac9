package com.ffnex.youxuan.mall.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ffnex.youxuan.common.core.util.R;
import com.ffnex.youxuan.common.log.annotation.SysLog;
import com.ffnex.youxuan.common.excel.annotation.ResponseExcel;
import com.ffnex.youxuan.common.excel.annotation.RequestExcel;
import com.ffnex.youxuan.mall.entity.PartnerOrderCommissionEntity;
import com.ffnex.youxuan.mall.service.PartnerOrderCommissionService;

import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import com.ffnex.youxuan.common.security.annotation.HasPermission;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 合伙人订单提成
 *
 * <AUTHOR>
 * @date 2025-08-19 21:49:27
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/partnerOrderCommission" )
@Tag(description = "partnerOrderCommission" , name = "合伙人订单提成管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class PartnerOrderCommissionController {

    private final  PartnerOrderCommissionService partnerOrderCommissionService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param partnerOrderCommission 合伙人订单提成
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @HasPermission("mall_partnerOrderCommission_view")
    public R getPartnerOrderCommissionPage(@ParameterObject Page page, @ParameterObject PartnerOrderCommissionEntity partnerOrderCommission) {
        LambdaQueryWrapper<PartnerOrderCommissionEntity> wrapper = Wrappers.lambdaQuery();
		wrapper.eq(Objects.nonNull(partnerOrderCommission.getPartnerId()),PartnerOrderCommissionEntity::getPartnerId,partnerOrderCommission.getPartnerId());
		wrapper.eq(Objects.nonNull(partnerOrderCommission.getOrderId()),PartnerOrderCommissionEntity::getOrderId,partnerOrderCommission.getOrderId());
		wrapper.eq(Objects.nonNull(partnerOrderCommission.getStatus()),PartnerOrderCommissionEntity::getStatus,partnerOrderCommission.getStatus());
        return R.ok(partnerOrderCommissionService.page(page, wrapper));
    }


    /**
     * 通过条件查询合伙人订单提成
     * @param partnerOrderCommission 查询条件
     * @return R  对象列表
     */
    @Operation(summary = "通过条件查询" , description = "通过条件查询对象" )
    @GetMapping("/details" )
    @HasPermission("mall_partnerOrderCommission_view")
    public R getDetails(@ParameterObject PartnerOrderCommissionEntity partnerOrderCommission) {
        return R.ok(partnerOrderCommissionService.list(Wrappers.query(partnerOrderCommission)));
    }

    /**
     * 新增合伙人订单提成
     * @param partnerOrderCommission 合伙人订单提成
     * @return R
     */
    @Operation(summary = "新增合伙人订单提成" , description = "新增合伙人订单提成" )
    @SysLog("新增合伙人订单提成" )
    @PostMapping
    @HasPermission("mall_partnerOrderCommission_add")
    public R save(@RequestBody PartnerOrderCommissionEntity partnerOrderCommission) {
        return R.ok(partnerOrderCommissionService.save(partnerOrderCommission));
    }

    /**
     * 修改合伙人订单提成
     * @param partnerOrderCommission 合伙人订单提成
     * @return R
     */
    @Operation(summary = "修改合伙人订单提成" , description = "修改合伙人订单提成" )
    @SysLog("修改合伙人订单提成" )
    @PutMapping
    @HasPermission("mall_partnerOrderCommission_edit")
    public R updateById(@RequestBody PartnerOrderCommissionEntity partnerOrderCommission) {
        return R.ok(partnerOrderCommissionService.updateById(partnerOrderCommission));
    }

    /**
     * 通过id删除合伙人订单提成
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除合伙人订单提成" , description = "通过id删除合伙人订单提成" )
    @SysLog("通过id删除合伙人订单提成" )
    @DeleteMapping
    @HasPermission("mall_partnerOrderCommission_del")
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(partnerOrderCommissionService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param partnerOrderCommission 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @HasPermission("mall_partnerOrderCommission_export")
    public List<PartnerOrderCommissionEntity> exportExcel(PartnerOrderCommissionEntity partnerOrderCommission,Long[] ids) {
        return partnerOrderCommissionService.list(Wrappers.lambdaQuery(partnerOrderCommission).in(ArrayUtil.isNotEmpty(ids), PartnerOrderCommissionEntity::getId, ids));
    }

    /**
     * 导入excel 表
     * @param partnerOrderCommissionList 对象实体列表
     * @param bindingResult 错误信息列表
     * @return ok fail
     */
    @PostMapping("/import")
    @HasPermission("mall_partnerOrderCommission_export")
    public R importExcel(@RequestExcel List<PartnerOrderCommissionEntity> partnerOrderCommissionList, BindingResult bindingResult) {
        return R.ok(partnerOrderCommissionService.saveBatch(partnerOrderCommissionList));
    }
}
