package com.ffnex.youxuan.mall.manager;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ffnex.youxuan.mall.entity.CustomerPricingRuleEntity;
import com.ffnex.youxuan.mall.entity.GoodsEntity;
import com.ffnex.youxuan.mall.entity.GoodsRecommendationEntity;
import com.ffnex.youxuan.mall.entity.GoodsServiceTagEntity;
import com.ffnex.youxuan.mall.entity.GoodsSkuEntity;
import com.ffnex.youxuan.mall.entity.TieredPricingRuleEntity;
import com.ffnex.youxuan.mall.mapper.CustomerPricingRuleMapper;
import com.ffnex.youxuan.mall.mapper.GoodsRecommendationMapper;
import com.ffnex.youxuan.mall.mapper.GoodsServiceTagMapper;
import com.ffnex.youxuan.mall.mapper.GoodsSkuMapper;
import com.ffnex.youxuan.mall.mapper.TieredPricingRuleMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

// 处理商品相关逻辑 提取自service中
@Component
@RequiredArgsConstructor
public class GoodsDomainManager {

    private final GoodsSkuMapper goodsSkuMapper;
    private final GoodsServiceTagMapper goodsServiceTagMapper;
    private final GoodsRecommendationMapper goodsRecommendationMapper;
    private final TieredPricingRuleMapper tieredPricingRuleMapper;
    private final CustomerPricingRuleMapper customerPricingRuleMapper;

    public void handleGoodsRelations(GoodsEntity goods) {
        // 处理商品SKU关系：增量更新（有ID的更新，无ID的插入，删除多余的）
        handleGoodsSkuRelations(goods);
        

        goodsServiceTagMapper.delete(Wrappers.lambdaQuery(GoodsServiceTagEntity.class).eq(GoodsServiceTagEntity::getGoodsId, goods.getId()));
        if (goods.getServiceTagIds() != null) {
            for (Long serviceTagId : goods.getServiceTagIds()) {
                GoodsServiceTagEntity goodsServiceTag = new GoodsServiceTagEntity();
                goodsServiceTag.setServiceTagId(serviceTagId);
                goodsServiceTag.setGoodsId(goods.getId());
                goodsServiceTagMapper.insert(goodsServiceTag);
            }
        }

        goodsRecommendationMapper.delete(Wrappers.lambdaQuery(GoodsRecommendationEntity.class).eq(GoodsRecommendationEntity::getGoodsId, goods.getId()));
        if (goods.getPositionIds() != null) {
            for (Long positionId : goods.getPositionIds()) {
                GoodsRecommendationEntity goodsRecommendationEntity = new GoodsRecommendationEntity();
                goodsRecommendationEntity.setGoodsId(goods.getId());
                goodsRecommendationEntity.setPositionId(positionId);
                goodsRecommendationMapper.insert(goodsRecommendationEntity);
            }
        }
    }

    /**
     * 处理商品SKU关系的增量更新
     * @param goods 商品实体
     */
    private void handleGoodsSkuRelations(GoodsEntity goods) {
        if (goods.getGoodsSkuList() == null || goods.getGoodsSkuList().isEmpty()) {
            // 如果新的SKU列表为空，删除所有现有的SKU
            goodsSkuMapper.delete(Wrappers.lambdaQuery(GoodsSkuEntity.class).eq(GoodsSkuEntity::getGoodsId, goods.getId()));
            return;
        }

        // 获取数据库中现有的SKU列表
        List<GoodsSkuEntity> existingSkus = goodsSkuMapper.selectList(
                Wrappers.lambdaQuery(GoodsSkuEntity.class).eq(GoodsSkuEntity::getGoodsId, goods.getId())
        );

        // 收集新SKU列表中有ID的SKU ID集合
        Set<Long> newSkuIds = goods.getGoodsSkuList().stream()
                .map(GoodsSkuEntity::getId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 找出需要删除的SKU（存在于数据库中但不在新列表中的）
        List<Long> skuIdsToDelete = existingSkus.stream()
                .map(GoodsSkuEntity::getId)
                .filter(id -> !newSkuIds.contains(id))
                .collect(Collectors.toList());

        // 删除多余的SKU
        if (!skuIdsToDelete.isEmpty()) {
            goodsSkuMapper.deleteByIds(skuIdsToDelete);
            // 同时删除这些SKU的定价规则
            for (Long skuId : skuIdsToDelete) {
                deleteSkuPricingRules(skuId);
            }
        }

        // 处理新SKU列表中的每个SKU
        for (GoodsSkuEntity goodsSku : goods.getGoodsSkuList()) {
            goodsSku.setGoodsId(goods.getId());

            if (goodsSku.getId() == null) {
                // 无ID的SKU：插入新记录
                goodsSkuMapper.insert(goodsSku);
            } else {
                // 有ID的SKU：更新现有记录
                goodsSkuMapper.updateById(goodsSku);
            }

            // 处理该SKU的定价规则
            handleSkuPricingRules(goodsSku);
        }
    }

    /**
     * 删除指定SKU的所有定价规则
     * @param skuId SKU ID
     */
    private void deleteSkuPricingRules(Long skuId) {
        // 删除阶梯定价规则
        tieredPricingRuleMapper.delete(Wrappers.lambdaQuery(TieredPricingRuleEntity.class)
                .eq(TieredPricingRuleEntity::getSkuId, skuId));

        // 删除客户定价规则
        customerPricingRuleMapper.delete(Wrappers.lambdaQuery(CustomerPricingRuleEntity.class)
                .eq(CustomerPricingRuleEntity::getSkuId, skuId));
    }

    /**
     * 处理SKU的定价规则
     * @param goodsSku SKU实体，必须已经插入数据库并拥有有效的ID
     */
    private void handleSkuPricingRules(GoodsSkuEntity goodsSku) {
        // 确保SKU ID不为空
        if (goodsSku.getId() == null) {
            throw new IllegalStateException("SKU ID不能为空，请确保SKU已经被正确插入数据库");
        }

        // 处理阶梯定价规则
        if (goodsSku.getTieredPricingRuleList() != null) {
            // 先删除该SKU的所有阶梯定价规则
            tieredPricingRuleMapper.delete(Wrappers.lambdaQuery(TieredPricingRuleEntity.class)
                    .eq(TieredPricingRuleEntity::getSkuId, goodsSku.getId()));

            // 重新插入阶梯定价规则
            for (TieredPricingRuleEntity tieredPricingRule : goodsSku.getTieredPricingRuleList()) {
                tieredPricingRule.setId(null); // 重置ID，确保作为新记录插入
                tieredPricingRule.setSkuId(goodsSku.getId());
                tieredPricingRuleMapper.insert(tieredPricingRule);
            }
        }

        // 处理客户定价规则
        if (goodsSku.getCustomerPricingRuleList() != null) {
            // 先删除该SKU的所有客户定价规则
            customerPricingRuleMapper.delete(Wrappers.lambdaQuery(CustomerPricingRuleEntity.class)
                    .eq(CustomerPricingRuleEntity::getSkuId, goodsSku.getId()));

            // 重新插入客户定价规则
            for (CustomerPricingRuleEntity customerPricingRule : goodsSku.getCustomerPricingRuleList()) {
                customerPricingRule.setId(null); // 重置ID，确保作为新记录插入
                customerPricingRule.setSkuId(goodsSku.getId());
                customerPricingRuleMapper.insert(customerPricingRule);
            }
        }
    }
}
