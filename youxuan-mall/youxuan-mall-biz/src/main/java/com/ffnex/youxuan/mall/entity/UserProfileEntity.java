package com.ffnex.youxuan.mall.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.ffnex.youxuan.common.core.util.TenantTable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 用户档案
 *
 * <AUTHOR>
 * @date 2025-08-19 21:45:27
 */
@Data
@TenantTable
@TableName("user_profile")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "用户档案")
public class UserProfileEntity extends Model<UserProfileEntity> {


	/**
	* ID
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="ID")
    private Long id;

	/**
	* 用户ID
	*/
    @Schema(description="用户ID")
    private Long userId;

	/**
	* 所属合伙人
	*/
    @Schema(description="所属合伙人")
    private Long partnerUserId;

	/**
	* 客户级别
	*/
    @Schema(description="客户级别")
    private Integer customerLevel;

	/**
	* 姓名
	*/
    @Schema(description="姓名")
    private String name;

	/**
	* 头像
	*/
    @Schema(description="头像")
    private String avatar;

	/**
	* 性别
	*/
    @Schema(description="性别")
    private Integer gender;

	/**
	* 手机号
	*/
    @Schema(description="手机号")
    private String phone;

	/**
	* 邮箱
	*/
    @Schema(description="邮箱")
    private String email;

	/**
	* 生日
	*/
    @Schema(description="生日")
    private LocalDate birthday;

	/**
	* 省
	*/
    @Schema(description="省")
    private String province;

	/**
	* 市
	*/
    @Schema(description="市")
    private String city;

	/**
	* 区
	*/
    @Schema(description="区")
    private String district;

	/**
	* 详细地址
	*/
    @Schema(description="详细地址")
    private String address;

	/**
	* 身份证号
	*/
    @Schema(description="身份证号")
    private String idCard;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 更新时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新时间")
    private LocalDateTime updateTime;

	/**
	* 租户
	*/
    @Schema(description="租户")
    private Long tenantId;

}