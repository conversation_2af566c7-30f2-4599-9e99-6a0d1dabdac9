package com.ffnex.youxuan.mall.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.ffnex.youxuan.common.core.util.TenantTable;
import com.github.yulichang.annotation.EntityMapping;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 商品SKU
 *
 * <AUTHOR>
 * @date 2025-01-24 15:55:11
 */
@Data
@TenantTable
@TableName("goods_sku")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "商品SKU")
public class GoodsSkuEntity extends Model<GoodsSkuEntity> {

 
	/**
	* id
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="id")
    private Long id;

	/**
	* 商品ID
	*/
    @Schema(description="商品ID")
    private Long goodsId;

	/**
	* sku名称
	*/
    @Schema(description="sku名称")
    private String name;

	/**
	* sku图片
	*/
    @Schema(description="sku图片")
    private String image;

	/**
	* 参考价
	*/
    @Schema(description="参考价")
    private BigDecimal originalPrice;

	/**
	 * 佣金
	 */
	@Schema(description = "佣金")
	private BigDecimal distributionCommission;

	/**
	* 实际价
	*/
    @Schema(description="实际价")
    private BigDecimal actualPrice;

	/**
	* 积分兑换价格比
	*/
    @Schema(description="积分兑换价格比")
    private BigDecimal creditMoneyRate;

	/**
	* 积分使用上限
	*/
    @Schema(description="积分使用上限")
    private BigDecimal creditLimit;

	/**
	* 销量
	*/
    @Schema(description="销量")
    private Integer sales;

	/**
	* 库存
	*/
    @Schema(description="库存")
    private Integer stock;

	/**
	* 排序
	*/
    @Schema(description="排序")
    private Integer weigh;

	/**
	* 软删除
	*/
    @TableLogic
    @Schema(description="软删除")
    private Integer deleted;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 更新时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新时间")
    private LocalDateTime updateTime;

	/**
	* 租户
	*/
    @Schema(description="租户")
    private Long tenantId;


	@TableField(exist = false)
	@EntityMapping(thisField = "goodsId",  joinField= "id")
	private GoodsEntity goods;

    @TableField(exist = false)
    private List<TieredPricingRuleEntity> tieredPricingRuleList;

    @TableField(exist = false)
    private List<CustomerPricingRuleEntity> customerPricingRuleList;
}