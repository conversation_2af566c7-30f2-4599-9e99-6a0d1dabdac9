package com.ffnex.youxuan.mall.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ffnex.youxuan.common.core.util.R;
import com.ffnex.youxuan.common.log.annotation.SysLog;
import com.ffnex.youxuan.common.excel.annotation.ResponseExcel;
import com.ffnex.youxuan.common.excel.annotation.RequestExcel;
import com.ffnex.youxuan.mall.entity.TieredPricingRuleEntity;
import com.ffnex.youxuan.mall.service.TieredPricingRuleService;

import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import com.ffnex.youxuan.common.security.annotation.HasPermission;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 阶梯定价规则
 *
 * <AUTHOR>
 * @date 2025-08-19 21:53:44
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tieredPricingRule" )
@Tag(description = "tieredPricingRule" , name = "阶梯定价规则管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TieredPricingRuleController {

    private final  TieredPricingRuleService tieredPricingRuleService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param tieredPricingRule 阶梯定价规则
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @HasPermission("mall_tieredPricingRule_view")
    public R getTieredPricingRulePage(@ParameterObject Page page, @ParameterObject TieredPricingRuleEntity tieredPricingRule) {
        LambdaQueryWrapper<TieredPricingRuleEntity> wrapper = Wrappers.lambdaQuery();
		wrapper.eq(Objects.nonNull(tieredPricingRule.getSkuId()),TieredPricingRuleEntity::getSkuId,tieredPricingRule.getSkuId());
        return R.ok(tieredPricingRuleService.page(page, wrapper));
    }


    /**
     * 通过条件查询阶梯定价规则
     * @param tieredPricingRule 查询条件
     * @return R  对象列表
     */
    @Operation(summary = "通过条件查询" , description = "通过条件查询对象" )
    @GetMapping("/details" )
    @HasPermission("mall_tieredPricingRule_view")
    public R getDetails(@ParameterObject TieredPricingRuleEntity tieredPricingRule) {
        return R.ok(tieredPricingRuleService.list(Wrappers.query(tieredPricingRule)));
    }

    /**
     * 新增阶梯定价规则
     * @param tieredPricingRule 阶梯定价规则
     * @return R
     */
    @Operation(summary = "新增阶梯定价规则" , description = "新增阶梯定价规则" )
    @SysLog("新增阶梯定价规则" )
    @PostMapping
    @HasPermission("mall_tieredPricingRule_add")
    public R save(@RequestBody TieredPricingRuleEntity tieredPricingRule) {
        return R.ok(tieredPricingRuleService.save(tieredPricingRule));
    }

    /**
     * 修改阶梯定价规则
     * @param tieredPricingRule 阶梯定价规则
     * @return R
     */
    @Operation(summary = "修改阶梯定价规则" , description = "修改阶梯定价规则" )
    @SysLog("修改阶梯定价规则" )
    @PutMapping
    @HasPermission("mall_tieredPricingRule_edit")
    public R updateById(@RequestBody TieredPricingRuleEntity tieredPricingRule) {
        return R.ok(tieredPricingRuleService.updateById(tieredPricingRule));
    }

    /**
     * 通过id删除阶梯定价规则
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除阶梯定价规则" , description = "通过id删除阶梯定价规则" )
    @SysLog("通过id删除阶梯定价规则" )
    @DeleteMapping
    @HasPermission("mall_tieredPricingRule_del")
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tieredPricingRuleService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param tieredPricingRule 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @HasPermission("mall_tieredPricingRule_export")
    public List<TieredPricingRuleEntity> exportExcel(TieredPricingRuleEntity tieredPricingRule,Long[] ids) {
        return tieredPricingRuleService.list(Wrappers.lambdaQuery(tieredPricingRule).in(ArrayUtil.isNotEmpty(ids), TieredPricingRuleEntity::getId, ids));
    }

    /**
     * 导入excel 表
     * @param tieredPricingRuleList 对象实体列表
     * @param bindingResult 错误信息列表
     * @return ok fail
     */
    @PostMapping("/import")
    @HasPermission("mall_tieredPricingRule_export")
    public R importExcel(@RequestExcel List<TieredPricingRuleEntity> tieredPricingRuleList, BindingResult bindingResult) {
        return R.ok(tieredPricingRuleService.saveBatch(tieredPricingRuleList));
    }
}
