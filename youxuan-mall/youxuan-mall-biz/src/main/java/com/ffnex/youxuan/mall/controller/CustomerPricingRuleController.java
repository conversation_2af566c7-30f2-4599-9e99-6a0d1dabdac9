package com.ffnex.youxuan.mall.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ffnex.youxuan.common.core.util.R;
import com.ffnex.youxuan.common.excel.annotation.RequestExcel;
import com.ffnex.youxuan.common.excel.annotation.ResponseExcel;
import com.ffnex.youxuan.common.log.annotation.SysLog;
import com.ffnex.youxuan.common.security.annotation.HasPermission;
import com.ffnex.youxuan.mall.entity.CustomerPricingRuleEntity;
import com.ffnex.youxuan.mall.service.CustomerPricingRuleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 客户定价规则
 *
 * <AUTHOR>
 * @date 2025-08-19 21:54:31
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/customerPricingRule" )
@Tag(description = "customerPricingRule" , name = "客户定价规则管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class CustomerPricingRuleController {

    private final  CustomerPricingRuleService customerPricingRuleService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param customerPricingRule 客户定价规则
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @HasPermission("mall_customerPricingRule_view")
    public R getCustomerPricingRulePage(@ParameterObject Page page, @ParameterObject CustomerPricingRuleEntity customerPricingRule) {
        LambdaQueryWrapper<CustomerPricingRuleEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(customerPricingRuleService.page(page, wrapper));
    }


    /**
     * 通过条件查询客户定价规则
     * @param customerPricingRule 查询条件
     * @return R  对象列表
     */
    @Operation(summary = "通过条件查询" , description = "通过条件查询对象" )
    @GetMapping("/details" )
    @HasPermission("mall_customerPricingRule_view")
    public R getDetails(@ParameterObject CustomerPricingRuleEntity customerPricingRule) {
        return R.ok(customerPricingRuleService.listDeep(Wrappers.query(customerPricingRule)));
    }

    /**
     * 新增客户定价规则
     * @param customerPricingRule 客户定价规则
     * @return R
     */
    @Operation(summary = "新增客户定价规则" , description = "新增客户定价规则" )
    @SysLog("新增客户定价规则" )
    @PostMapping
    @HasPermission("mall_customerPricingRule_add")
    public R save(@RequestBody CustomerPricingRuleEntity customerPricingRule) {
        return R.ok(customerPricingRuleService.save(customerPricingRule));
    }

    /**
     * 修改客户定价规则
     * @param customerPricingRule 客户定价规则
     * @return R
     */
    @Operation(summary = "修改客户定价规则" , description = "修改客户定价规则" )
    @SysLog("修改客户定价规则" )
    @PutMapping
    @HasPermission("mall_customerPricingRule_edit")
    public R updateById(@RequestBody CustomerPricingRuleEntity customerPricingRule) {
        return R.ok(customerPricingRuleService.updateById(customerPricingRule));
    }

    /**
     * 通过id删除客户定价规则
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除客户定价规则" , description = "通过id删除客户定价规则" )
    @SysLog("通过id删除客户定价规则" )
    @DeleteMapping
    @HasPermission("mall_customerPricingRule_del")
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(customerPricingRuleService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param customerPricingRule 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @HasPermission("mall_customerPricingRule_export")
    public List<CustomerPricingRuleEntity> exportExcel(CustomerPricingRuleEntity customerPricingRule,Long[] ids) {
        return customerPricingRuleService.list(Wrappers.lambdaQuery(customerPricingRule).in(ArrayUtil.isNotEmpty(ids), CustomerPricingRuleEntity::getId, ids));
    }

    /**
     * 导入excel 表
     * @param customerPricingRuleList 对象实体列表
     * @param bindingResult 错误信息列表
     * @return ok fail
     */
    @PostMapping("/import")
    @HasPermission("mall_customerPricingRule_export")
    public R importExcel(@RequestExcel List<CustomerPricingRuleEntity> customerPricingRuleList, BindingResult bindingResult) {
        return R.ok(customerPricingRuleService.saveBatch(customerPricingRuleList));
    }
}
