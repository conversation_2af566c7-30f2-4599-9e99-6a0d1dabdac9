package com.ffnex.youxuan.mall.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.ffnex.youxuan.common.core.util.TenantTable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 合伙人押金记录
 *
 * <AUTHOR>
 * @date 2025-08-19 21:41:04
 */
@Data
@TenantTable
@TableName("partner_deposit_log")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "合伙人押金记录")
public class PartnerDepositLogEntity extends Model<PartnerDepositLogEntity> {


	/**
	* ID
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="ID")
    private Long id;

	/**
	* 合伙人ID
	*/
    @Schema(description="合伙人ID")
    private Long partnerId;

	/**
	* 类型
	*/
    @Schema(description="类型")
    private Integer type;

	/**
	* 前金额
	*/
    @Schema(description="前金额")
    private BigDecimal beforeAmount;

	/**
	* 变动金额
	*/
    @Schema(description="变动金额")
    private BigDecimal changeAmount;

	/**
	* 后金额
	*/
    @Schema(description="后金额")
    private BigDecimal afterAmount;

	/**
	* 备注
	*/
    @Schema(description="备注")
    private String remark;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 租户
	*/
    @Schema(description="租户")
    private Long tenantId;
}