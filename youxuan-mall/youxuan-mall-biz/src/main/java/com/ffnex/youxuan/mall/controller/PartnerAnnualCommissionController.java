package com.ffnex.youxuan.mall.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ffnex.youxuan.common.core.util.R;
import com.ffnex.youxuan.common.log.annotation.SysLog;
import com.ffnex.youxuan.common.excel.annotation.ResponseExcel;
import com.ffnex.youxuan.common.excel.annotation.RequestExcel;
import com.ffnex.youxuan.mall.entity.PartnerAnnualCommissionEntity;
import com.ffnex.youxuan.mall.service.PartnerAnnualCommissionService;

import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import com.ffnex.youxuan.common.security.annotation.HasPermission;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 合伙人年度提成
 *
 * <AUTHOR>
 * @date 2025-08-19 21:52:11
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/partnerAnnualCommission" )
@Tag(description = "partnerAnnualCommission" , name = "合伙人年度提成管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class PartnerAnnualCommissionController {

    private final  PartnerAnnualCommissionService partnerAnnualCommissionService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param partnerAnnualCommission 合伙人年度提成
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @HasPermission("mall_partnerAnnualCommission_view")
    public R getPartnerAnnualCommissionPage(@ParameterObject Page page, @ParameterObject PartnerAnnualCommissionEntity partnerAnnualCommission) {
        LambdaQueryWrapper<PartnerAnnualCommissionEntity> wrapper = Wrappers.lambdaQuery();
		wrapper.eq(Objects.nonNull(partnerAnnualCommission.getYear()),PartnerAnnualCommissionEntity::getYear,partnerAnnualCommission.getYear());
		wrapper.eq(Objects.nonNull(partnerAnnualCommission.getStatus()),PartnerAnnualCommissionEntity::getStatus,partnerAnnualCommission.getStatus());
        return R.ok(partnerAnnualCommissionService.page(page, wrapper));
    }


    /**
     * 通过条件查询合伙人年度提成
     * @param partnerAnnualCommission 查询条件
     * @return R  对象列表
     */
    @Operation(summary = "通过条件查询" , description = "通过条件查询对象" )
    @GetMapping("/details" )
    @HasPermission("mall_partnerAnnualCommission_view")
    public R getDetails(@ParameterObject PartnerAnnualCommissionEntity partnerAnnualCommission) {
        return R.ok(partnerAnnualCommissionService.list(Wrappers.query(partnerAnnualCommission)));
    }

    /**
     * 新增合伙人年度提成
     * @param partnerAnnualCommission 合伙人年度提成
     * @return R
     */
    @Operation(summary = "新增合伙人年度提成" , description = "新增合伙人年度提成" )
    @SysLog("新增合伙人年度提成" )
    @PostMapping
    @HasPermission("mall_partnerAnnualCommission_add")
    public R save(@RequestBody PartnerAnnualCommissionEntity partnerAnnualCommission) {
        return R.ok(partnerAnnualCommissionService.save(partnerAnnualCommission));
    }

    /**
     * 修改合伙人年度提成
     * @param partnerAnnualCommission 合伙人年度提成
     * @return R
     */
    @Operation(summary = "修改合伙人年度提成" , description = "修改合伙人年度提成" )
    @SysLog("修改合伙人年度提成" )
    @PutMapping
    @HasPermission("mall_partnerAnnualCommission_edit")
    public R updateById(@RequestBody PartnerAnnualCommissionEntity partnerAnnualCommission) {
        return R.ok(partnerAnnualCommissionService.updateById(partnerAnnualCommission));
    }

    /**
     * 通过id删除合伙人年度提成
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除合伙人年度提成" , description = "通过id删除合伙人年度提成" )
    @SysLog("通过id删除合伙人年度提成" )
    @DeleteMapping
    @HasPermission("mall_partnerAnnualCommission_del")
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(partnerAnnualCommissionService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param partnerAnnualCommission 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @HasPermission("mall_partnerAnnualCommission_export")
    public List<PartnerAnnualCommissionEntity> exportExcel(PartnerAnnualCommissionEntity partnerAnnualCommission,Long[] ids) {
        return partnerAnnualCommissionService.list(Wrappers.lambdaQuery(partnerAnnualCommission).in(ArrayUtil.isNotEmpty(ids), PartnerAnnualCommissionEntity::getId, ids));
    }

    /**
     * 导入excel 表
     * @param partnerAnnualCommissionList 对象实体列表
     * @param bindingResult 错误信息列表
     * @return ok fail
     */
    @PostMapping("/import")
    @HasPermission("mall_partnerAnnualCommission_export")
    public R importExcel(@RequestExcel List<PartnerAnnualCommissionEntity> partnerAnnualCommissionList, BindingResult bindingResult) {
        return R.ok(partnerAnnualCommissionService.saveBatch(partnerAnnualCommissionList));
    }
}
